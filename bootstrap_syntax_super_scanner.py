#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Bootstrap 4/5 语法差异超级扫描工具
================================

这个工具将全面扫描 C:\StudentsCMSSP\app\templates 下所有模板文件，
检测Bootstrap 4和Bootstrap 5之间的语法差异，并生成详细的修复报告。

功能特性：
1. 全面检测所有Bootstrap 4/5语法差异
2. 支持HTML属性、CSS类名、JavaScript API差异检测
3. 生成详细的修复建议和代码示例
4. 支持批量修复功能
5. 生成可视化报告

作者: Augment Agent
日期: 2025-06-16
"""

import os
import re
import json
import html
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Set
from collections import defaultdict, Counter
import logging

class BootstrapSyntaxSuperScanner:
    """Bootstrap语法差异超级扫描器"""
    
    def __init__(self, templates_root: str = "app/templates"):
        """初始化扫描器"""
        self.templates_root = Path(templates_root)

        # 排除测试页面的模式
        self.exclude_patterns = [
            r".*test.*\.html$",
            r".*demo.*\.html$",
            r".*example.*\.html$",
            r".*mobile.*test.*\.html$",
            r".*layout.*test.*\.html$",
            r".*theme.*test.*\.html$",
            r".*theme.*demo.*\.html$",
            r".*mobile.*demo.*\.html$",
            r".*csp.*\.html$",
            r".*check.*resources.*\.html$"
        ]
        self.scan_results = {
            "scan_info": {
                "timestamp": datetime.now().isoformat(),
                "templates_root": str(self.templates_root),
                "total_files": 0,
                "scanned_files": 0,
                "files_with_issues": 0
            },
            "files": {},
            "summary": {
                "bootstrap4_syntax": 0,
                "bootstrap5_syntax": 0,
                "mixed_syntax": 0,
                "total_issues": 0,
                "critical_issues": 0,
                "warning_issues": 0
            },
            "issue_categories": defaultdict(list),
            "fix_suggestions": []
        }
        
        # 设置日志
        self.setup_logging()
        
        # Bootstrap 4 到 5 的语法映射
        self.syntax_mappings = self.get_syntax_mappings()
        
        # 检测模式
        self.detection_patterns = self.get_detection_patterns()
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'bootstrap_syntax_scan_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def get_syntax_mappings(self) -> Dict:
        """获取Bootstrap 4到5的语法映射"""
        return {
            # HTML属性映射
            "attributes": {
                "data-toggle": "data-bs-toggle",
                "data-target": "data-bs-target", 
                "data-dismiss": "data-bs-dismiss",
                "data-slide": "data-bs-slide",
                "data-slide-to": "data-bs-slide-to",
                "data-ride": "data-bs-ride",
                "data-interval": "data-bs-interval",
                "data-pause": "data-bs-pause",
                "data-wrap": "data-bs-wrap",
                "data-keyboard": "data-bs-keyboard",
                "data-backdrop": "data-bs-backdrop",
                "data-focus": "data-bs-focus",
                "data-spy": "data-bs-spy",
                "data-offset": "data-bs-offset",
                "data-method": "data-bs-method",
                "data-target": "data-bs-target",
                "data-parent": "data-bs-parent",
                "data-container": "data-bs-container",
                "data-placement": "data-bs-placement",
                "data-trigger": "data-bs-trigger",
                "data-delay": "data-bs-delay",
                "data-html": "data-bs-html",
                "data-selector": "data-bs-selector",
                "data-template": "data-bs-template",
                "data-title": "data-bs-title",
                "data-content": "data-bs-content",
                "data-boundary": "data-bs-boundary",
                "data-reference": "data-bs-reference",
                "data-display": "data-bs-display",
                "data-auto-close": "data-bs-auto-close"
            },
            
            # CSS类名映射
            "classes": {
                # 按钮相关
                "btn-block": "d-grid",
                "btn-group-toggle": "",  # 移除
                
                # 表单相关
                "form-group": "mb-3",
                "form-row": "row g-3",
                "form-inline": "d-flex",
                "input-group-prepend": "",  # 移除
                "input-group-append": "",   # 移除
                "input-group-text": "input-group-text",  # 保持不变但结构改变
                "custom-control": "form-check",
                "custom-control-input": "form-check-input", 
                "custom-control-label": "form-check-label",
                "custom-checkbox": "form-check",
                "custom-radio": "form-check",
                "custom-switch": "form-check form-switch",
                "custom-select": "form-select",
                "custom-file": "form-control",
                "custom-file-input": "form-control",
                "custom-file-label": "form-label",
                "custom-range": "form-range",
                
                # 文本和排版
                "text-left": "text-start",
                "text-right": "text-end", 
                "float-left": "float-start",
                "float-right": "float-end",
                "border-left": "border-start",
                "border-right": "border-end",
                "rounded-left": "rounded-start",
                "rounded-right": "rounded-end",
                "ml-": "ms-",  # margin-left
                "mr-": "me-",  # margin-right
                "pl-": "ps-",  # padding-left
                "pr-": "pe-",  # padding-right
                
                # 网格系统
                "no-gutters": "g-0",
                
                # 卡片
                "card-deck": "row row-cols-1 row-cols-md-3 g-4",
                "card-columns": "row row-cols-1 row-cols-md-2 row-cols-xl-3",
                
                # 媒体对象
                "media": "d-flex",
                "media-object": "flex-shrink-0",
                "media-body": "flex-grow-1 ms-3",
                
                # 工具类
                "sr-only": "visually-hidden",
                "sr-only-focusable": "visually-hidden-focusable",
                
                # 关闭按钮
                "close": "btn-close",
                
                # 下拉菜单
                "dropdown-menu-left": "dropdown-menu-start",
                "dropdown-menu-right": "dropdown-menu-end",
                "dropleft": "dropstart",
                "dropright": "dropend",
                
                # 导航
                "navbar-expand": "navbar-expand-lg",  # 需要指定断点
                
                # 徽章
                "badge-pill": "rounded-pill",
                
                # 进度条
                "progress-bar-striped": "progress-bar progress-bar-striped",
                
                # 列表组
                "list-group-item-action": "list-group-item list-group-item-action"
            },
            
            # JavaScript API映射
            "javascript": {
                "$(element).modal('show')": "new bootstrap.Modal(element).show()",
                "$(element).modal('hide')": "bootstrap.Modal.getInstance(element).hide()",
                "$(element).dropdown('toggle')": "new bootstrap.Dropdown(element).toggle()",
                "$(element).collapse('show')": "new bootstrap.Collapse(element).show()",
                "$(element).collapse('hide')": "bootstrap.Collapse.getInstance(element).hide()",
                "$(element).tab('show')": "new bootstrap.Tab(element).show()",
                "$(element).tooltip('show')": "new bootstrap.Tooltip(element).show()",
                "$(element).popover('show')": "new bootstrap.Popover(element).show()",
                "$(element).alert('close')": "bootstrap.Alert.getInstance(element).close()",
                "$(element).button('toggle')": "new bootstrap.Button(element).toggle()",
                "$(element).carousel('next')": "bootstrap.Carousel.getInstance(element).next()",
                "$(element).carousel('prev')": "bootstrap.Carousel.getInstance(element).prev()",
                "$(element).scrollspy('refresh')": "bootstrap.ScrollSpy.getInstance(element).refresh()"
            }
        }
        
    def get_detection_patterns(self) -> Dict:
        """获取检测模式"""
        return {
            # Bootstrap 4 特征模式
            "bootstrap4_patterns": [
                r'\bdata-toggle\s*=',
                r'\bdata-target\s*=',
                r'\bdata-dismiss\s*=',
                r'\bbtn-block\b',
                r'\bform-group\b',
                r'\bform-row\b',
                r'\binput-group-prepend\b',
                r'\binput-group-append\b',
                r'\bcustom-control\b',
                r'\bcustom-checkbox\b',
                r'\bcustom-radio\b',
                r'\bcustom-switch\b',
                r'\bcustom-select\b',
                r'\bcustom-file\b',
                r'\btext-left\b',
                r'\btext-right\b',
                r'\bfloat-left\b',
                r'\bfloat-right\b',
                r'\bml-\d+\b',
                r'\bmr-\d+\b',
                r'\bpl-\d+\b',
                r'\bpr-\d+\b',
                r'\bsr-only\b',
                r'\bclose\b',
                r'\bbadge-pill\b',
                r'\bmedia\b',
                r'\bmedia-object\b',
                r'\bmedia-body\b',
                r'\bcard-deck\b',
                r'\bcard-columns\b',
                r'\bno-gutters\b'
            ],
            
            # Bootstrap 5 特征模式
            "bootstrap5_patterns": [
                r'\bdata-bs-toggle\s*=',
                r'\bdata-bs-target\s*=',
                r'\bdata-bs-dismiss\s*=',
                r'\bd-grid\b',
                r'\bmb-3\b',
                r'\brow g-3\b',
                r'\bform-check\b',
                r'\bform-check-input\b',
                r'\bform-check-label\b',
                r'\bform-switch\b',
                r'\bform-select\b',
                r'\bform-range\b',
                r'\btext-start\b',
                r'\btext-end\b',
                r'\bfloat-start\b',
                r'\bfloat-end\b',
                r'\bms-\d+\b',
                r'\bme-\d+\b',
                r'\bps-\d+\b',
                r'\bpe-\d+\b',
                r'\bvisually-hidden\b',
                r'\bbtn-close\b',
                r'\brounded-pill\b',
                r'\bg-0\b'
            ],
            
            # 混合使用检测
            "mixed_usage_patterns": [
                (r'\bdata-toggle\s*=', r'\bdata-bs-toggle\s*='),
                (r'\btext-left\b', r'\btext-start\b'),
                (r'\btext-right\b', r'\btext-end\b'),
                (r'\bform-group\b', r'\bmb-3\b'),
                (r'\bcustom-control\b', r'\bform-check\b')
            ]
        }

    def scan_all_templates(self) -> Dict:
        """扫描所有模板文件"""
        self.logger.info(f"🔍 开始扫描模板目录: {self.templates_root}")

        if not self.templates_root.exists():
            self.logger.error(f"❌ 模板目录不存在: {self.templates_root}")
            return self.scan_results

        # 获取所有HTML文件，排除测试页面
        all_html_files = list(self.templates_root.rglob("*.html"))
        html_files = []

        for file_path in all_html_files:
            file_str = str(file_path).lower()
            is_excluded = False

            for pattern in self.exclude_patterns:
                if re.match(pattern, file_str, re.IGNORECASE):
                    is_excluded = True
                    self.logger.info(f"🚫 跳过测试页面: {file_path.relative_to(self.templates_root)}")
                    break

            if not is_excluded:
                html_files.append(file_path)

        self.scan_results["scan_info"]["total_files"] = len(html_files)
        self.scan_results["scan_info"]["excluded_files"] = len(all_html_files) - len(html_files)

        self.logger.info(f"📁 发现 {len(all_html_files)} 个HTML文件，排除 {len(all_html_files) - len(html_files)} 个测试页面")
        self.logger.info(f"📋 将扫描 {len(html_files)} 个实际模板文件")

        for file_path in html_files:
            try:
                self.scan_single_file(file_path)
                self.scan_results["scan_info"]["scanned_files"] += 1
            except Exception as e:
                self.logger.error(f"❌ 扫描文件失败 {file_path}: {str(e)}")

        # 生成汇总统计
        self.generate_summary()

        self.logger.info(f"✅ 扫描完成，共处理 {self.scan_results['scan_info']['scanned_files']} 个文件")
        return self.scan_results

    def scan_single_file(self, file_path: Path):
        """扫描单个文件"""
        relative_path = file_path.relative_to(self.templates_root)

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
            except:
                self.logger.warning(f"⚠️ 无法读取文件: {file_path}")
                return

        file_result = {
            "path": str(relative_path),
            "size": len(content),
            "lines": len(content.splitlines()),
            "bootstrap4_issues": [],
            "bootstrap5_issues": [],
            "mixed_usage": [],
            "critical_issues": [],
            "warnings": [],
            "suggestions": [],
            "bootstrap_version": "unknown"
        }

        # 检测Bootstrap版本倾向
        file_result["bootstrap_version"] = self.detect_bootstrap_version(content)

        # 检测Bootstrap 4语法
        self.detect_bootstrap4_syntax(content, file_result)

        # 检测Bootstrap 5语法
        self.detect_bootstrap5_syntax(content, file_result)

        # 检测混合使用
        self.detect_mixed_usage(content, file_result)

        # 检测关键问题
        self.detect_critical_issues(content, file_result)

        # 生成修复建议
        self.generate_fix_suggestions(content, file_result)

        # 如果有问题，记录到结果中
        total_issues = (len(file_result["bootstrap4_issues"]) +
                       len(file_result["bootstrap5_issues"]) +
                       len(file_result["mixed_usage"]) +
                       len(file_result["critical_issues"]))

        if total_issues > 0:
            self.scan_results["files"][str(relative_path)] = file_result
            self.scan_results["scan_info"]["files_with_issues"] += 1

            # 更新问题分类
            for issue in file_result["bootstrap4_issues"]:
                self.scan_results["issue_categories"]["bootstrap4"].append({
                    "file": str(relative_path),
                    "issue": issue
                })

            for issue in file_result["bootstrap5_issues"]:
                self.scan_results["issue_categories"]["bootstrap5"].append({
                    "file": str(relative_path),
                    "issue": issue
                })

            for issue in file_result["mixed_usage"]:
                self.scan_results["issue_categories"]["mixed"].append({
                    "file": str(relative_path),
                    "issue": issue
                })

            for issue in file_result["critical_issues"]:
                self.scan_results["issue_categories"]["critical"].append({
                    "file": str(relative_path),
                    "issue": issue
                })

    def detect_bootstrap_version(self, content: str) -> str:
        """检测文件的Bootstrap版本倾向"""
        bs4_score = 0
        bs5_score = 0

        # 检测Bootstrap 4特征
        for pattern in self.detection_patterns["bootstrap4_patterns"]:
            matches = re.findall(pattern, content, re.IGNORECASE)
            bs4_score += len(matches)

        # 检测Bootstrap 5特征
        for pattern in self.detection_patterns["bootstrap5_patterns"]:
            matches = re.findall(pattern, content, re.IGNORECASE)
            bs5_score += len(matches)

        if bs4_score > bs5_score:
            return "bootstrap4_dominant"
        elif bs5_score > bs4_score:
            return "bootstrap5_dominant"
        elif bs4_score > 0 and bs5_score > 0:
            return "mixed"
        else:
            return "unknown"

    def detect_bootstrap4_syntax(self, content: str, file_result: Dict):
        """检测Bootstrap 4语法"""
        lines = content.splitlines()

        for line_num, line in enumerate(lines, 1):
            # 检测属性
            for bs4_attr, bs5_attr in self.syntax_mappings["attributes"].items():
                if re.search(rf'\b{re.escape(bs4_attr)}\s*=', line, re.IGNORECASE):
                    file_result["bootstrap4_issues"].append({
                        "type": "attribute",
                        "line": line_num,
                        "content": line.strip(),
                        "issue": bs4_attr,
                        "suggestion": bs5_attr,
                        "severity": "high" if bs4_attr in ["data-toggle", "data-target", "data-dismiss"] else "medium"
                    })

            # 检测CSS类
            for bs4_class, bs5_class in self.syntax_mappings["classes"].items():
                if bs4_class.endswith("-"):
                    # 处理前缀类（如ml-, mr-等）
                    pattern = rf'\b{re.escape(bs4_class)}\d+\b'
                else:
                    pattern = rf'\b{re.escape(bs4_class)}\b'

                if re.search(pattern, line, re.IGNORECASE):
                    file_result["bootstrap4_issues"].append({
                        "type": "class",
                        "line": line_num,
                        "content": line.strip(),
                        "issue": bs4_class,
                        "suggestion": bs5_class,
                        "severity": "medium"
                    })

    def detect_bootstrap5_syntax(self, content: str, file_result: Dict):
        """检测Bootstrap 5语法"""
        lines = content.splitlines()

        for line_num, line in enumerate(lines, 1):
            # 检测Bootstrap 5特有的属性
            for pattern in self.detection_patterns["bootstrap5_patterns"]:
                if re.search(pattern, line, re.IGNORECASE):
                    file_result["bootstrap5_issues"].append({
                        "type": "bootstrap5_syntax",
                        "line": line_num,
                        "content": line.strip(),
                        "pattern": pattern,
                        "severity": "info"
                    })

    def detect_mixed_usage(self, content: str, file_result: Dict):
        """检测混合使用情况"""
        for bs4_pattern, bs5_pattern in self.detection_patterns["mixed_usage_patterns"]:
            bs4_matches = re.findall(bs4_pattern, content, re.IGNORECASE)
            bs5_matches = re.findall(bs5_pattern, content, re.IGNORECASE)

            if bs4_matches and bs5_matches:
                file_result["mixed_usage"].append({
                    "type": "mixed_usage",
                    "bootstrap4_pattern": bs4_pattern,
                    "bootstrap5_pattern": bs5_pattern,
                    "bootstrap4_count": len(bs4_matches),
                    "bootstrap5_count": len(bs5_matches),
                    "severity": "high"
                })

    def detect_critical_issues(self, content: str, file_result: Dict):
        """检测关键问题"""
        lines = content.splitlines()

        # 关键的功能性问题
        critical_patterns = {
            r'data-toggle\s*=\s*["\']dropdown["\']': "下拉菜单可能无法工作",
            r'data-toggle\s*=\s*["\']modal["\']': "模态框可能无法打开",
            r'data-toggle\s*=\s*["\']collapse["\']': "折叠组件可能无法工作",
            r'data-toggle\s*=\s*["\']tab["\']': "标签页可能无法切换",
            r'data-dismiss\s*=\s*["\']modal["\']': "模态框关闭按钮可能无效",
            r'data-dismiss\s*=\s*["\']alert["\']': "警告框关闭按钮可能无效",
            r'\$\([^)]+\)\.modal\s*\(': "jQuery模态框API在Bootstrap 5中不兼容",
            r'\$\([^)]+\)\.dropdown\s*\(': "jQuery下拉菜单API在Bootstrap 5中不兼容",
            r'\$\([^)]+\)\.collapse\s*\(': "jQuery折叠API在Bootstrap 5中不兼容"
        }

        for line_num, line in enumerate(lines, 1):
            for pattern, description in critical_patterns.items():
                if re.search(pattern, line, re.IGNORECASE):
                    file_result["critical_issues"].append({
                        "type": "critical",
                        "line": line_num,
                        "content": line.strip(),
                        "pattern": pattern,
                        "description": description,
                        "severity": "critical"
                    })

    def generate_fix_suggestions(self, content: str, file_result: Dict):
        """生成修复建议"""
        suggestions = []

        # 基于检测到的问题生成建议
        if file_result["bootstrap4_issues"]:
            suggestions.append({
                "type": "attribute_fix",
                "description": "将Bootstrap 4属性更新为Bootstrap 5语法",
                "priority": "high",
                "auto_fixable": True
            })

        if file_result["critical_issues"]:
            suggestions.append({
                "type": "functionality_fix",
                "description": "修复可能导致功能失效的关键问题",
                "priority": "critical",
                "auto_fixable": False
            })

        if file_result["mixed_usage"]:
            suggestions.append({
                "type": "consistency_fix",
                "description": "统一Bootstrap版本语法，避免混合使用",
                "priority": "high",
                "auto_fixable": True
            })

        file_result["suggestions"] = suggestions

    def generate_summary(self):
        """生成汇总统计"""
        summary = self.scan_results["summary"]

        # 统计各种语法使用情况
        for file_data in self.scan_results["files"].values():
            if file_data["bootstrap_version"] == "bootstrap4_dominant":
                summary["bootstrap4_syntax"] += 1
            elif file_data["bootstrap_version"] == "bootstrap5_dominant":
                summary["bootstrap5_syntax"] += 1
            elif file_data["bootstrap_version"] == "mixed":
                summary["mixed_syntax"] += 1

            summary["total_issues"] += (
                len(file_data["bootstrap4_issues"]) +
                len(file_data["bootstrap5_issues"]) +
                len(file_data["mixed_usage"])
            )

            summary["critical_issues"] += len(file_data["critical_issues"])
            summary["warning_issues"] += len(file_data["warnings"])

    def generate_detailed_report(self) -> str:
        """生成详细的HTML报告"""
        html_report = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bootstrap 4/5 语法差异扫描报告</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        h1 {{ color: #333; border-bottom: 3px solid #007bff; padding-bottom: 10px; }}
        h2 {{ color: #495057; margin-top: 30px; }}
        h3 {{ color: #6c757d; }}
        .summary {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }}
        .stat-card {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; text-align: center; }}
        .stat-card.critical {{ background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); }}
        .stat-card.warning {{ background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%); }}
        .stat-card.success {{ background: linear-gradient(135deg, #48dbfb 0%, #0abde3 100%); }}
        .stat-number {{ font-size: 2em; font-weight: bold; }}
        .stat-label {{ font-size: 0.9em; opacity: 0.9; }}
        .file-list {{ margin: 20px 0; }}
        .file-item {{ background: #f8f9fa; margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }}
        .file-item.critical {{ border-left-color: #dc3545; }}
        .file-item.warning {{ border-left-color: #ffc107; }}
        .issue-list {{ margin: 10px 0; }}
        .issue-item {{ background: white; margin: 5px 0; padding: 10px; border-radius: 3px; border-left: 3px solid #6c757d; }}
        .issue-item.high {{ border-left-color: #dc3545; }}
        .issue-item.medium {{ border-left-color: #ffc107; }}
        .issue-item.low {{ border-left-color: #28a745; }}
        .code {{ background: #f1f3f4; padding: 2px 6px; border-radius: 3px; font-family: 'Courier New', monospace; font-size: 0.9em; }}
        .suggestion {{ background: #e7f3ff; padding: 10px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #007bff; }}
        .fix-button {{ background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }}
        .fix-button:hover {{ background: #218838; }}
        .tabs {{ display: flex; border-bottom: 2px solid #dee2e6; margin: 20px 0; }}
        .tab {{ padding: 10px 20px; cursor: pointer; border-bottom: 2px solid transparent; }}
        .tab.active {{ border-bottom-color: #007bff; color: #007bff; }}
        .tab-content {{ display: none; }}
        .tab-content.active {{ display: block; }}
        .progress-bar {{ width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; }}
        .progress-fill {{ height: 100%; background: linear-gradient(90deg, #007bff, #0056b3); transition: width 0.3s ease; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Bootstrap 4/5 语法差异扫描报告</h1>

        <div class="summary">
            <div class="stat-card">
                <div class="stat-number">{self.scan_results['scan_info']['total_files']}</div>
                <div class="stat-label">总文件数</div>
            </div>
            <div class="stat-card critical">
                <div class="stat-number">{self.scan_results['scan_info']['files_with_issues']}</div>
                <div class="stat-label">有问题的文件</div>
            </div>
            <div class="stat-card warning">
                <div class="stat-number">{self.scan_results['summary']['total_issues']}</div>
                <div class="stat-label">总问题数</div>
            </div>
            <div class="stat-card success">
                <div class="stat-number">{self.scan_results['summary']['critical_issues']}</div>
                <div class="stat-label">关键问题</div>
            </div>
        </div>

        <h2>📊 扫描统计</h2>
        <p><strong>扫描时间:</strong> {self.scan_results['scan_info']['timestamp']}</p>
        <p><strong>扫描目录:</strong> <code>{self.scan_results['scan_info']['templates_root']}</code></p>

        <div class="progress-bar">
            <div class="progress-fill" style="width: {(self.scan_results['scan_info']['scanned_files'] / max(self.scan_results['scan_info']['total_files'], 1)) * 100:.1f}%"></div>
        </div>
        <p>扫描进度: {self.scan_results['scan_info']['scanned_files']}/{self.scan_results['scan_info']['total_files']} 文件</p>

        <div class="tabs">
            <div class="tab active" onclick="showTab('overview')">概览</div>
            <div class="tab" onclick="showTab('files')">文件详情</div>
            <div class="tab" onclick="showTab('issues')">问题分类</div>
            <div class="tab" onclick="showTab('fixes')">修复建议</div>
        </div>

        <div id="overview" class="tab-content active">
            <h3>版本分布</h3>
            <ul>
                <li>Bootstrap 4 主导: {self.scan_results['summary']['bootstrap4_syntax']} 个文件</li>
                <li>Bootstrap 5 主导: {self.scan_results['summary']['bootstrap5_syntax']} 个文件</li>
                <li>混合使用: {self.scan_results['summary']['mixed_syntax']} 个文件</li>
            </ul>
        </div>

        <div id="files" class="tab-content">
            <h3>文件详情</h3>
            <div class="file-list">
        """

        # 添加文件详情
        for file_path, file_data in self.scan_results["files"].items():
            severity_class = "critical" if file_data["critical_issues"] else ("warning" if file_data["bootstrap4_issues"] else "")

            html_report += f"""
                <div class="file-item {severity_class}">
                    <h4>📄 {file_path}</h4>
                    <p><strong>版本倾向:</strong> {file_data['bootstrap_version']}</p>
                    <p><strong>文件大小:</strong> {file_data['size']} 字符, {file_data['lines']} 行</p>

                    {self._generate_issues_html(file_data)}
                </div>
            """

        html_report += """
            </div>
        </div>

        <div id="issues" class="tab-content">
            <h3>问题分类统计</h3>
        """

        # 添加问题分类
        for category, issues in self.scan_results["issue_categories"].items():
            if issues:
                html_report += f"""
                    <h4>{category.title()} 问题 ({len(issues)} 个)</h4>
                    <div class="issue-list">
                """
                for issue in issues[:10]:  # 只显示前10个
                    html_report += f"""
                        <div class="issue-item">
                            <strong>{issue['file']}</strong>: {issue['issue']}
                        </div>
                    """
                if len(issues) > 10:
                    html_report += f"<p>... 还有 {len(issues) - 10} 个类似问题</p>"
                html_report += "</div>"

        html_report += """
        </div>

        <div id="fixes" class="tab-content">
            <h3>🛠️ 自动修复工具</h3>
            <div class="suggestion">
                <h4>一键修复选项</h4>
                <button class="fix-button" onclick="alert('功能开发中...')">修复所有 data-toggle 属性</button>
                <button class="fix-button" onclick="alert('功能开发中...')">修复所有 CSS 类名</button>
                <button class="fix-button" onclick="alert('功能开发中...')">生成修复脚本</button>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有标签的活动状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');

            // 激活选中的标签
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
        """

        return html_report

    def _generate_issues_html(self, file_data: Dict) -> str:
        """生成文件问题的HTML"""
        html = ""

        if file_data["critical_issues"]:
            html += "<h5>🚨 关键问题:</h5><div class='issue-list'>"
            for issue in file_data["critical_issues"]:
                html += f"""
                    <div class="issue-item high">
                        <strong>行 {issue['line']}:</strong> {issue['description']}<br>
                        <code>{html.escape(issue['content'])}</code>
                    </div>
                """
            html += "</div>"

        if file_data["bootstrap4_issues"]:
            html += "<h5>⚠️ Bootstrap 4 语法:</h5><div class='issue-list'>"
            for issue in file_data["bootstrap4_issues"][:5]:  # 只显示前5个
                severity_class = issue.get('severity', 'medium')
                html += f"""
                    <div class="issue-item {severity_class}">
                        <strong>行 {issue['line']}:</strong> {issue['issue']} → {issue['suggestion']}<br>
                        <code>{html.escape(issue['content'])}</code>
                    </div>
                """
            if len(file_data["bootstrap4_issues"]) > 5:
                html += f"<p>... 还有 {len(file_data['bootstrap4_issues']) - 5} 个类似问题</p>"
            html += "</div>"

        if file_data["mixed_usage"]:
            html += "<h5>🔄 混合使用:</h5><div class='issue-list'>"
            for issue in file_data["mixed_usage"]:
                html += f"""
                    <div class="issue-item high">
                        同时使用 Bootstrap 4 和 5 语法
                    </div>
                """
            html += "</div>"

        return html

    def save_results(self, output_dir: str = "."):
        """保存扫描结果"""
        output_path = Path(output_dir)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存JSON结果
        json_file = output_path / f"bootstrap_syntax_scan_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.scan_results, f, ensure_ascii=False, indent=2)

        # 保存HTML报告
        html_file = output_path / f"bootstrap_syntax_report_{timestamp}.html"
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(self.generate_detailed_report())

        self.logger.info(f"📄 结果已保存:")
        self.logger.info(f"  JSON: {json_file}")
        self.logger.info(f"  HTML: {html_file}")

        return json_file, html_file

    def generate_fix_script(self) -> str:
        """生成自动修复脚本"""
        script_content = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
\"\"\"
Bootstrap 4 到 5 自动修复脚本
由 Bootstrap 语法扫描器自动生成
\"\"\"

import re
import os
from pathlib import Path

def fix_bootstrap_syntax(file_path):
    \"\"\"修复单个文件的Bootstrap语法\"\"\"
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    original_content = content

    # 修复属性
    replacements = {
"""

        # 添加属性替换
        for bs4_attr, bs5_attr in self.syntax_mappings["attributes"].items():
            script_content += f'        r"\\b{bs4_attr}\\s*=": "{bs5_attr}=",\n'

        script_content += """    }

    for pattern, replacement in replacements.items():
        content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)

    # 修复CSS类
    class_replacements = {
"""

        # 添加类替换
        for bs4_class, bs5_class in self.syntax_mappings["classes"].items():
            if bs5_class:  # 跳过空的替换
                script_content += f'        r"\\b{bs4_class}\\b": "{bs5_class}",\n'

        script_content += """    }

    for pattern, replacement in class_replacements.items():
        content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)

    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 已修复: {file_path}")
        return True
    return False

def main():
    templates_dir = Path("app/templates")
    fixed_count = 0

    for html_file in templates_dir.rglob("*.html"):
        if fix_bootstrap_syntax(html_file):
            fixed_count += 1

    print(f"\\n🎉 修复完成，共处理 {fixed_count} 个文件")

if __name__ == "__main__":
    main()
"""

        return script_content


def main():
    """主函数"""
    print("🚀 Bootstrap 4/5 语法差异超级扫描器")
    print("=" * 50)

    # 创建扫描器实例
    scanner = BootstrapSyntaxSuperScanner()

    # 执行扫描
    results = scanner.scan_all_templates()

    # 保存结果
    json_file, html_file = scanner.save_results()

    # 生成修复脚本
    fix_script_content = scanner.generate_fix_script()
    fix_script_file = Path("bootstrap_auto_fix.py")
    with open(fix_script_file, 'w', encoding='utf-8') as f:
        f.write(fix_script_content)

    print(f"\n📋 扫描汇总:")
    print(f"  总文件数: {results['scan_info']['total_files']}")
    print(f"  有问题文件: {results['scan_info']['files_with_issues']}")
    print(f"  总问题数: {results['summary']['total_issues']}")
    print(f"  关键问题: {results['summary']['critical_issues']}")
    print(f"\n🛠️ 自动修复脚本已生成: {fix_script_file}")
    print(f"📊 详细报告请查看: {html_file}")


if __name__ == "__main__":
    main()
